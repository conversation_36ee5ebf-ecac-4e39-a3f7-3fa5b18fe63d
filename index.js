const express = require("express");
const app = express();

require("dotenv").config();
const PORT = process.env.PORT || 3000;

// middleware
app.use(express.json());
const blog = require("./routes/blog.js");
// mount
app.use("/api/v1", blog);

const connectWithDb = require("./config/database.js");
connectWithDb();

// start the server
app.listen(PORT, ()=>{
    console.log(`APP is started at port no ${PORT}`);
})

app.get("/",(req,res)=>{
    res.send("This is homepage");
})