// import model
const Post = require("../models/post.js");
const Like = require("../models/like.js");
// business logic
exports.createLike = async(req,res) =>{
    try{
        const {post, user} = req.body;
        const like = new Like({
            post,user,
        });
        const saveLike = await like.save();
        const updatedPost = await Post.findByIdAndUpdate(post,{$push: {likes: saveLike._id}}, {new: true})
        .populate("likes")
        .exec();
        res.json({
            post: updatedPost,
        });
    }
    catch(error){
        return res.status(500).json({
            error:"Error while creating post",
        });    
    }
};

exports.dummyLink = (req,res) => {
    res.send("This is your Dummy Page");
};