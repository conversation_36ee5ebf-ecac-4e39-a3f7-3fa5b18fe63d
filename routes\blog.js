const express = require("express");
const router = express.Router();

// Import Controller
const {dummyLink, likePost} = require("../controllers/likeController.js");
const {createComment} = require("../controllers/commentController.js");
const {createPost, getAllPosts} = require("../controllers/postController.js");

// Mapping Create
router.get("/dummyroute", dummyLink);
router.post("/comments/creates", createComment);
router.post("/posts/creates", createPost);
router.post("/posts/getall", getAllPosts);
router.post("/likes/like", likePost);

// export
module.exports = router;
