<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="6d48e1ed-ad88-4bb0-ae1e-20397b1f1353" name="Default" comment="" />
    <ignored path="env.iws" />
    <ignored path=".idea/workspace.xml" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager" flattened_view="true" show_ignored="false" />
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
    <option name="REVERSE_PATCH" value="false" />
  </component>
  <component name="DaemonCodeAnalyzer">
    <disable_hints />
  </component>
  <component name="FavoritesManager">
    <favorites_list name="env" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file leaf-file-name="Readme.md" pinned="false" current="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/examples/simple/Readme.md">
          <provider selected="true" editor-type-id="text-editor">
            <state line="4" column="3" selection-start="50" selection-end="50" vertical-scroll-proportion="-2.7272727">
              <folding />
            </state>
          </provider>
          <provider editor-type-id="MarkdownPreviewEditor">
            <state />
          </provider>
        </entry>
      </file>
      <file leaf-file-name="env.js" pinned="false" current="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/env.js">
          <provider selected="true" editor-type-id="text-editor">
            <state line="24" column="69" selection-start="381" selection-end="388" vertical-scroll-proportion="0.0">
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="package.json" pinned="false" current="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state line="2" column="21" selection-start="41" selection-end="41" vertical-scroll-proportion="0.0">
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="env.test.js" pinned="false" current="true" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/test/env.test.js">
          <provider selected="true" editor-type-id="text-editor">
            <state line="24" column="2" selection-start="437" selection-end="437" vertical-scroll-proportion="0.42007002">
              <folding />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindManager">
    <FindUsagesManager>
      <setting name="OPEN_NEW_TAB" value="false" />
    </FindUsagesManager>
  </component>
  <component name="IdeDocumentHistory">
    <option name="changedFiles">
      <list>
        <option value="$PROJECT_DIR$/test/fixtures/env.json" />
        <option value="$PROJECT_DIR$/index.js" />
        <option value="$PROJECT_DIR$/.gitignore" />
        <option value="$PROJECT_DIR$/.travis.yml" />
        <option value="$PROJECT_DIR$/examples/simple/env.json" />
        <option value="$PROJECT_DIR$/examples/simple/index.js" />
        <option value="$PROJECT_DIR$/examples/simple/Readme.md" />
        <option value="$PROJECT_DIR$/Readme.md" />
        <option value="$PROJECT_DIR$/lib/env.js" />
        <option value="$PROJECT_DIR$/package.json" />
        <option value="$PROJECT_DIR$/test/env.test.js" />
      </list>
    </option>
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="false">
    <OptionsSetting value="true" id="Add" />
    <OptionsSetting value="true" id="Remove" />
    <OptionsSetting value="true" id="Checkout" />
    <OptionsSetting value="true" id="Update" />
    <OptionsSetting value="true" id="Status" />
    <OptionsSetting value="true" id="Edit" />
    <ConfirmationsSetting value="0" id="Add" />
    <ConfirmationsSetting value="0" id="Remove" />
  </component>
  <component name="ProjectReloadState">
    <option name="STATE" value="0" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1" splitterProportion="0.5">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents ProjectPane="false" />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
    </navigator>
    <panes>
      <pane id="Favorites" />
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="fixtures" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="lib" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="examples" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="simple" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="examples" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
  </component>
  <component name="RunManager">
    <list size="0" />
  </component>
  <component name="ShelveChangesManager" show_recycled="false" />
  <component name="SvnConfiguration" maxAnnotateRevisions="500">
    <option name="USER" value="" />
    <option name="PASSWORD" value="" />
    <option name="mySSHConnectionTimeout" value="30000" />
    <option name="mySSHReadTimeout" value="30000" />
    <option name="LAST_MERGED_REVISION" />
    <option name="MERGE_DRY_RUN" value="false" />
    <option name="MERGE_DIFF_USE_ANCESTRY" value="true" />
    <option name="UPDATE_LOCK_ON_DEMAND" value="false" />
    <option name="IGNORE_SPACES_IN_MERGE" value="false" />
    <option name="DETECT_NESTED_COPIES" value="true" />
    <option name="CHECK_NESTED_FOR_QUICK_MERGE" value="false" />
    <option name="IGNORE_SPACES_IN_ANNOTATE" value="true" />
    <option name="SHOW_MERGE_SOURCES_IN_ANNOTATE" value="true" />
    <option name="FORCE_UPDATE" value="false" />
    <myIsUseDefaultProxy>false</myIsUseDefaultProxy>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6d48e1ed-ad88-4bb0-ae1e-20397b1f1353" name="Default" comment="" />
      <created>1322230347131</created>
      <updated>1322230347131</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="22" width="1920" height="1004" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info id="Changes" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Phing Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.25" sideWeight="0.5" order="1" side_tool="true" content_ui="tabs" />
      <window_info id="Dependency Viewer" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" weight="0.11223944" sideWeight="0.97742665" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="OFFER_MOVE_TO_ANOTHER_CHANGELIST_ON_PARTIAL_COMMIT" value="true" />
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="CHECK_NEW_TODO" value="true" />
    <option name="myTodoPanelSettings">
      <value>
        <are-packages-shown value="false" />
        <are-modules-shown value="false" />
        <flatten-packages value="false" />
        <is-autoscroll-to-source value="false" />
      </value>
    </option>
    <option name="PERFORM_UPDATE_IN_BACKGROUND" value="true" />
    <option name="PERFORM_COMMIT_IN_BACKGROUND" value="true" />
    <option name="PERFORM_EDIT_IN_BACKGROUND" value="true" />
    <option name="PERFORM_CHECKOUT_IN_BACKGROUND" value="true" />
    <option name="PERFORM_ADD_REMOVE_IN_BACKGROUND" value="true" />
    <option name="PERFORM_ROLLBACK_IN_BACKGROUND" value="false" />
    <option name="CHECK_LOCALLY_CHANGED_CONFLICTS_IN_BACKGROUND" value="false" />
    <option name="ENABLE_BACKGROUND_PROCESSES" value="false" />
    <option name="CHANGED_ON_SERVER_INTERVAL" value="60" />
    <option name="SHOW_ONLY_CHANGED_IN_SELECTION_DIFF" value="true" />
    <option name="CHECK_COMMIT_MESSAGE_SPELLING" value="true" />
    <option name="DEFAULT_PATCH_EXTENSION" value="patch" />
    <option name="FORCE_NON_EMPTY_COMMENT" value="false" />
    <option name="LAST_COMMIT_MESSAGE" />
    <option name="MAKE_NEW_CHANGELIST_ACTIVE" value="true" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_FILES_UP_TO_DATE_BEFORE_COMMIT" value="false" />
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false" />
    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8" />
    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5" />
    <option name="ACTIVE_VCS_NAME" />
    <option name="UPDATE_GROUP_BY_PACKAGES" value="false" />
    <option name="UPDATE_GROUP_BY_CHANGELIST" value="false" />
    <option name="SHOW_FILE_HISTORY_AS_TREE" value="false" />
    <option name="FILE_HISTORY_SPLITTER_PROPORTION" value="0.6" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/.gitignore">
      <provider selected="true" editor-type-id="text-editor">
        <state line="6" column="0" selection-start="59" selection-end="59" vertical-scroll-proportion="0.1559792" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.travis.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state line="2" column="0" selection-start="27" selection-end="27" vertical-scroll-proportion="0.05199307" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/fixtures/env.json">
      <provider selected="true" editor-type-id="text-editor">
        <state line="2" column="15" selection-start="0" selection-end="40" vertical-scroll-proportion="0.0" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/examples/simple/env.json">
      <provider selected="true" editor-type-id="text-editor">
        <state line="1" column="2" selection-start="4" selection-end="4" vertical-scroll-proportion="0.0" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/examples/simple/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state line="2" column="0" selection-start="56" selection-end="122" vertical-scroll-proportion="0.0" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state line="16" column="25" selection-start="193" selection-end="200" vertical-scroll-proportion="0.0" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/Readme.md">
      <provider selected="true" editor-type-id="text-editor">
        <state line="9" column="132" selection-start="362" selection-end="377" vertical-scroll-proportion="0.24538745" />
      </provider>
      <provider editor-type-id="MarkdownPreviewEditor">
        <state />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/examples/simple/Readme.md">
      <provider selected="true" editor-type-id="text-editor">
        <state line="4" column="3" selection-start="50" selection-end="50" vertical-scroll-proportion="-2.7272727">
          <folding />
        </state>
      </provider>
      <provider editor-type-id="MarkdownPreviewEditor">
        <state />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state line="2" column="21" selection-start="41" selection-end="41" vertical-scroll-proportion="0.0">
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/env.js">
      <provider selected="true" editor-type-id="text-editor">
        <state line="24" column="69" selection-start="381" selection-end="388" vertical-scroll-proportion="0.0">
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/env.test.js">
      <provider selected="true" editor-type-id="text-editor">
        <state line="24" column="2" selection-start="437" selection-end="437" vertical-scroll-proportion="0.42007002">
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>

