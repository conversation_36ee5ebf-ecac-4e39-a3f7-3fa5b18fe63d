{"name": "env", "version": "0.0.2", "description": "Environment variable manager", "keywords": ["process", "environment", "env"], "author": "<PERSON> <<EMAIL>> (http://dshaw.com)", "repository": {"type": "git", "url": "https://github.com/dshaw/env"}, "issues": {"url": "https://github.com/dshaw/env/issues"}, "main": "index", "directories": {"lib": "lib", "test": "test"}, "devDependencies": {"tap": "0.0.x"}, "scripts": {"test": "tap ./test/*.js"}, "engines": {"node": ">= 0.5.9"}}