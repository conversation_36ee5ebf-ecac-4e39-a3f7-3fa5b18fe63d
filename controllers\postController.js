const Post = require("../models/post.js");

exports.createPost = async(req,res) =>{
    try{
        const {title, body} = req.body;
        const post = new Post({
            title,body,
        })
        const savedPost = await post.save();
        res.json({
            post:savedPost
        })
    }
    catch(error){
        return res.status(500).json({
            error:"Error while creating post",
        });
    }
};

exports.getAllPosts = async(rec,res) =>{
    try{
        const posts = await Post.find({}).populate("comment").exec();
        res.json({
            posts,
        });
    }
    catch(error){
        return res.status(500).json({
            error:"Error while creating post",
        });
    }
};